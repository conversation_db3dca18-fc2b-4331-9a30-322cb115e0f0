---
type: "always_apply"
---

Whenever you want to ask a question, always call the MCP interactive_feedback.
Whenever you’re about to complete a user request, call the MCP interactive_feedback instead of simply ending the process. Keep calling MCP until the user’s feedback is empty, then end the request.

##下面是具体内容：
核心原则： 在处理任何任务时，AI 必须在关键节点使用 interactive_feedback 工具与用户进行互动，而不是在没有用户明确反馈的情况下擅自进行重大决策或完成任务阶段。

触发条件与行为：

任务开始和需求确认时： 在初步理解用户需求或开始新任务后，调用 interactive_feedback 工具，总结对任务的理解和初步计划，并询问用户是否有进一步的澄清或指示。

interactive_feedback 摘要应包含：对用户请求的理解、任务目标、以及需要用户确认的任何假设或不确定性。

完成任务的某个主要阶段或步骤时： 在完成计划中的一个重要步骤、一个功能模块的实现、一个 Bug 的修复阶段（例如，分析完成、修复代码编写完成等），或任何您认为可以向用户报告进展并获取反馈的时机，调用 interactive_feedback 工具。

interactive_feedback 摘要应包含：已完成的工作内容、当前的状态、以及下一步计划。

遇到任何不确定、模棱两可或需要决策的情况时： 如果在任务执行过程中遇到任何规则冲突、技术难题、需要选择不同的实现方案、或者对下一步行动不确定时，必须立即停止并调用 interactive_feedback 工具向用户报告问题并寻求指导。

interactive_feedback 摘要应包含：遇到的具体问题或不确定性、导致不确定的原因、以及您认为可能 的解决方案或下一步选项（如果适用）。

即将完成整个用户请求时： 在任务的所有执行阶段都已完成，即将向用户交付最终结果或结束对话之前，必须调用 interactive_feedback 工具进行最终报告和确认。

interactive_feedback 摘要应包含：整个任务的最终完成状态、结果摘要、以及是否还有其他需要处理的事项。

interactive_feedback 摘要应包含：失败的工具名称、错误信息（如果可用）、失败发生时的上下文、以及您建议如何尝试解决或绕过问题。

重要： 在执行上述规则时，务必在 interactive_feedback 的 summary 参数中提供清晰、简洁且具有足够上下文的信息，以便用户能够快速理解当前状态并提供有效的反馈。

